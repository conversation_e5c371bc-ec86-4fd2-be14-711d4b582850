from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.extensions import db
from app.modules.inventory.models_supplier import Supplier
from app.utils.decorators import inventory_access_required as permission_required
from . import bp
from .forms import SupplierForm

@bp.route('/suppliers')
@login_required
@permission_required
def suppliers():
    """Liste des fournisseurs"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    
    query = Supplier.query.filter_by(owner_id=current_user.id)
    
    if search:
        query = query.filter(Supplier.name.ilike(f'%{search}%'))
    
    suppliers = query.order_by(Supplier.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('inventory/suppliers/list.html',
                         title='Fournisseurs',
                         suppliers=suppliers,
                         search=search,
                         per_page=per_page)

@bp.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_supplier():
    """Ajouter un fournisseur"""
    form = SupplierForm()
    
    if form.validate_on_submit():
        supplier = Supplier(
            name=form.name.data,
            category_id=form.category_id.data if form.category_id.data != 0 else None,
            contact_name=form.contact_name.data,
            email=form.email.data,
            phone=form.phone.data,
            website=form.website.data,
            tax_id=form.tax_id.data,
            payment_terms=form.payment_terms.data,
            address=form.address.data,
            notes=form.notes.data,
            rating=form.rating.data,
            is_active=form.is_active.data,
            owner_id=current_user.id
        )
        
        db.session.add(supplier)
        db.session.commit()
        flash('Fournisseur ajouté avec succès!', 'success')
        return redirect(url_for('inventory.suppliers'))
    
    return render_template('inventory/suppliers/form.html',
                         title='Ajouter un fournisseur',
                         form=form)

@bp.route('/suppliers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_supplier(id):
    """Modifier un fournisseur"""
    supplier = Supplier.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Initialiser le formulaire avec les valeurs du fournisseur
    if request.method == 'GET':
        form = SupplierForm()
        form.name.data = supplier.name
        form.category_id.data = supplier.category_id or 0
        form.contact_name.data = supplier.contact_name
        form.email.data = supplier.email
        form.phone.data = supplier.phone
        form.website.data = supplier.website
        form.tax_id.data = supplier.tax_id
        form.payment_terms.data = supplier.payment_terms
        form.address.data = supplier.address
        form.notes.data = supplier.notes
        form.rating.data = supplier.rating
        form.is_active.data = supplier.is_active
    else:
        form = SupplierForm()

    if form.validate_on_submit():
        supplier.name = form.name.data
        supplier.category_id = form.category_id.data if form.category_id.data != 0 else None
        supplier.contact_name = form.contact_name.data
        supplier.email = form.email.data
        supplier.phone = form.phone.data
        supplier.website = form.website.data
        supplier.tax_id = form.tax_id.data
        supplier.payment_terms = form.payment_terms.data
        supplier.address = form.address.data
        supplier.notes = form.notes.data
        supplier.rating = form.rating.data
        supplier.is_active = form.is_active.data
        
        db.session.commit()
        flash('Fournisseur modifié avec succès!', 'success')
        return redirect(url_for('inventory.suppliers'))
    
    return render_template('inventory/suppliers/form.html',
                         title='Modifier un fournisseur',
                         form=form,
                         supplier=supplier)

@bp.route('/suppliers/<int:id>')
@login_required
@permission_required('can_manage_inventory')
def supplier_details(id):
    """Afficher les détails d'un fournisseur"""
    supplier = Supplier.query.filter_by(id=id, owner_id=current_user.get_owner_id).first_or_404()
    return render_template('inventory/suppliers/details.html', supplier=supplier)

@bp.route('/suppliers/<int:id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_supplier(id):
    """Supprimer un fournisseur"""
    supplier = Supplier.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Vérifier si des ingrédients sont associés à ce fournisseur
    ingredients_count = supplier.ingredients.count()
    if ingredients_count > 0:
        flash(f'Impossible de supprimer ce fournisseur, {ingredients_count} ingrédient(s) y sont associés.', 'error')
        return redirect(url_for('inventory.suppliers'))
    
    db.session.delete(supplier)
    db.session.commit()
    flash('Fournisseur supprimé avec succès!', 'success')
    return redirect(url_for('inventory.suppliers')) 