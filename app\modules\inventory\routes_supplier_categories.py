from flask import render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from app import db
from app.modules.inventory.models_supplier_category import SupplierCategory
from app.modules.inventory.forms import SupplierCategoryForm
from . import bp

@bp.route('/supplier-categories')
@login_required
def supplier_categories():
    """Liste des catégories de fournisseurs"""
    categories = SupplierCategory.query.filter_by(
        owner_id=current_user.get_owner_id,
        is_active=True
    ).order_by(SupplierCategory.name).all()
    
    return render_template('inventory/supplier_categories/list.html', categories=categories)

@bp.route('/supplier-categories/add', methods=['GET', 'POST'])
@login_required
def add_supplier_category():
    """Ajouter une catégorie de fournisseur"""
    form = SupplierCategoryForm()
    
    if form.validate_on_submit():
        category = SupplierCategory(
            name=form.name.data,
            description=form.description.data,
            color=form.color.data,
            icon=form.icon.data,
            owner_id=current_user.get_owner_id
        )
        
        db.session.add(category)
        db.session.commit()
        flash('Catégorie de fournisseur ajoutée avec succès!', 'success')
        return redirect(url_for('inventory.supplier_categories'))
    
    return render_template('inventory/supplier_categories/form.html',
                         title='Ajouter une catégorie de fournisseur',
                         form=form)

@bp.route('/supplier-categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_supplier_category(id):
    """Modifier une catégorie de fournisseur"""
    category = SupplierCategory.query.filter_by(
        id=id, 
        owner_id=current_user.get_owner_id
    ).first_or_404()
    
    form = SupplierCategoryForm(obj=category)
    
    if form.validate_on_submit():
        form.populate_obj(category)
        db.session.commit()
        flash('Catégorie de fournisseur modifiée avec succès!', 'success')
        return redirect(url_for('inventory.supplier_categories'))
    
    return render_template('inventory/supplier_categories/form.html',
                         title='Modifier la catégorie de fournisseur',
                         form=form,
                         category=category)

@bp.route('/supplier-categories/<int:id>/delete', methods=['POST'])
@login_required
def delete_supplier_category(id):
    """Supprimer une catégorie de fournisseur"""
    category = SupplierCategory.query.filter_by(
        id=id, 
        owner_id=current_user.get_owner_id
    ).first_or_404()
    
    # Vérifier s'il y a des fournisseurs dans cette catégorie
    if category.supplier_count > 0:
        flash('Impossible de supprimer cette catégorie car elle contient des fournisseurs.', 'error')
        return redirect(url_for('inventory.supplier_categories'))
    
    # Marquer comme inactif au lieu de supprimer
    category.is_active = False
    db.session.commit()
    flash('Catégorie de fournisseur supprimée avec succès!', 'success')
    return redirect(url_for('inventory.supplier_categories'))

@bp.route('/supplier-categories/<int:id>')
@login_required
def supplier_category_details(id):
    """Détails d'une catégorie de fournisseur"""
    category = SupplierCategory.query.filter_by(
        id=id, 
        owner_id=current_user.get_owner_id
    ).first_or_404()
    
    suppliers = category.active_suppliers
    
    return render_template('inventory/supplier_categories/details.html',
                         category=category,
                         suppliers=suppliers)
